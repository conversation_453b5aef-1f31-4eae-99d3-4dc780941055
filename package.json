{"name": "purchase-flow-front", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "3.13.8", "@graphql-codegen/near-operation-file-preset": "3.1.0", "@hookform/resolvers": "5.1.1", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tooltip": "1.2.7", "@react-spring/types": "10.0.1", "@rudderstack/analytics-js": "3.21.0", "@sentry/cli": "2.46.0", "@sentry/react": "9.35.0", "@sentry/vite-plugin": "3.5.0", "@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-react-swc": "3.10.2", "apollo-upload-client": "18.0.1", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "4.1.0", "embla-carousel-autoplay": "8.6.0", "embla-carousel-fade": "8.6.0", "embla-carousel-react": "8.6.0", "env-cmd": "10.1.0", "graphql": "16.11.0", "i18next": "25.3.1", "i18next-browser-languagedetector": "8.2.0", "i18next-locize-backend": "7.0.4", "js-cookie": "^3.0.5", "lodash": "4.17.21", "lucide-react": "0.525.0", "posthog-js": "1.256.2", "react": "19.1.0", "react-dom": "19.1.0", "react-ga4": "^2.1.0", "react-helmet-async": "2.0.5", "react-hook-form": "7.60.0", "react-i18next": "15.6.0", "react-loading-skeleton": "3.5.0", "react-number-format": "5.4.4", "react-popper-tooltip": "4.4.2", "react-router-dom": "7.6.3", "react-toastify": "11.0.5", "react-use": "17.6.0", "sass": "1.89.2", "sonner": "2.0.6", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "uuid": "11.1.0", "vite": "7.0.2", "vite-tsconfig-paths": "5.1.4", "zod": "3.25.74"}, "scripts": {"dev": "env-cmd -e local vite", "dev1": "env-cmd -e dekker-dev1 vite", "build": "tsc && env-cmd -e local vite build", "build:dekker-dev1": "env-cmd -e dekker-dev1 vite build", "build:dekker-dev3": "env-cmd -e dekker-dev3 vite build", "build:staging-lt": "env-cmd -e staging-lt vite build", "build:production-et": "env-cmd -e production-et vite build", "build:production-lt-new": "env-cmd -e production-lt-new vite build", "build:production-lv": "env-cmd -e production-lv vite build", "codegen:purchase-flow": "graphql-codegen -c codegen-purchase-flow.ts", "codegen:core": "graphql-codegen -c codegen-core.ts", "print-version": "node src/scripts/print-version.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "lint-and-format": "pnpm run lint:fix && pnpm run format", "ts-check": "tsc --noEmit", "git-hooks-install": "lefthook install", "tailwind": "npx tailwindcss -i ./src/styles/tailwind.css -o ./src/styles/index.css --watch"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.1", "@graphql-codegen/cli": "5.0.7", "@graphql-codegen/typescript-operations": "4.6.1", "@graphql-codegen/typescript-react-apollo": "4.3.3", "@savvywombat/tailwindcss-grid-areas": "4.0.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/apollo-upload-client": "18.0.0", "@types/jest": "30.0.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "4.17.20", "@types/node": "24.0.10", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "autoprefixer": "10.4.21", "eslint": "9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.3.0", "i": "0.3.7", "postcss": "8.5.6", "prettier": "^3.6.2", "tailwindcss": "4.1.11", "typescript": "5.8.3", "vite-plugin-svgr": "4.3.0", "vite-plugin-webfont-dl": "3.10.5"}}