.wrapper {
  @apply relative;
}

.container {
  @apply grid border border-neutral-200 rounded-2xl transition-shadow overflow-hidden;
}

.content {
  @apply flex items-center gap-x-4 p-4;
}

.info {
  @apply grid gap-1;
}

.icon {
  @apply [grid-area:icon] self-center;
}

.title {
  @apply [grid-area:title];
}

.description {
  @apply [grid-area:description];
}

.reduceMonthlyPayment {
  @apply absolute top-6 right-4;
}
