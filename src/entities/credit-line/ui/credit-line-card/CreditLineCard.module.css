.container {
  @apply grid
    [grid-template-areas:'info'_'progress'_'after']
    data-[with-cta=true]:[grid-template-areas:'info'_'progress'_'after'_'ctaContainer']
    grid-cols-1
    items-center
    gap-x-4
    gap-y-6
    pb-[4.5rem]
    bg-primary-white
    md:grid-cols-[1fr,auto]
    md:rounded-3xl
    md:border
    md:border-neutral-200
    md:p-8
    md:[grid-template-areas:'info_after'_'progress_progress']
    md:data-[with-cta=true]:[grid-template-areas:'info_after'_'progress_progress'_'ctaContainer_ctaContainer'];
}

.info {
  @apply [grid-area:info]
    grid
    gap-1.5;
}

.progress {
  @apply [grid-area:progress];
}

.after {
  @apply [grid-area:after]
    mt-2
    md:mt-0;
}

.ctaContainer {
  @apply grid 
    gap-2 
    md:col-span-2;
}
