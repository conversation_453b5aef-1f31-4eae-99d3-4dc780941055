// @ts-expect-error plugin written in js
import tailwindcss_grid_areas from '@savvywombat/tailwindcss-grid-areas';
import tailwindcss from '@tailwindcss/vite';
import type { Config } from 'tailwindcss';
import tailwindcss_animate from 'tailwindcss-animate';

const colorMixAlphaValueWithCustomProperty = (
  customPropertyName: string,
) => `color-mix(
  in srgb,
  var(${customPropertyName}),
  transparent calc(100% - 100% * <alpha-value>)
)`;

const config = {
  darkMode: 'class',
  content: [
    './index.html',
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    screens: {
      xs: '390px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1440px',
    },
    container: {
      center: true,
      padding: '2rem',
    },
    fontWeight: {
      normal: '400',
      semibold: '600',
      medium: '500',
      bold: '700',
    },
    fontFamily: {
      inter: ['Inter', 'sans-serif'],
    },
    extend: {
      borderRadius: {
        xxl: '1.5rem',
        '1xl': '0.875rem',
      },
      boxShadow: {
        container: '0px 5px 20px 0px rgba(42, 40, 135, 0.05)',
        hover: '0px 6px 15px 0px rgba(42, 40, 135, 0.07)',
        'inset-dialog-gray': '0px 0px 0px 1px var(--color-neutral-200) inset',
      },
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        neutral: {
          900: 'var(--color-neutral-900)',
          800: 'var(--color-neutral-800)',
          700: 'var(--color-neutral-700)',
          600: 'var(--color-neutral-600)',
          500: 'var(--color-neutral-500)',
          400: 'var(--color-neutral-400)',
          300: 'var(--color-neutral-300)',
          200: 'var(--color-neutral-200)',
          100: 'var(--color-neutral-100)',
          50: 'var(--color-neutral-50)',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
          brand02: colorMixAlphaValueWithCustomProperty(
            '--color-primary-brand-02',
          ),
          black: colorMixAlphaValueWithCustomProperty('--color-primary-black'),
          white: colorMixAlphaValueWithCustomProperty('--color-primary-white'),
        },
        typography: {
          black: colorMixAlphaValueWithCustomProperty('--color-primary-black'),
          neutral400: colorMixAlphaValueWithCustomProperty(
            '--color-neutral-400',
          ),
          neutral300: colorMixAlphaValueWithCustomProperty(
            '--color-neutral-300',
          ),
          white: colorMixAlphaValueWithCustomProperty('--color-primary-white'),
        },
        borders: {
          neutral200: colorMixAlphaValueWithCustomProperty(
            '--color-neutral-200',
          ),
        },
        system: {
          green: colorMixAlphaValueWithCustomProperty('--color-system-green'),
          yellow: colorMixAlphaValueWithCustomProperty('--color-system-green'),
          orange: colorMixAlphaValueWithCustomProperty('--color-system-orange'),
          red: colorMixAlphaValueWithCustomProperty('--color-system-red'),
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      transitionDelay: {
        '3000': '3000ms',
      },
      keyframes: {
        loader: {
          to: {
            opacity: '0.1',
            transform: 'translate3d(0, -0.25rem, 0)',
          },
        },
        'lds-spinner': {
          '0%': {
            opacity: '1',
          },
          '100%': {
            opacity: '0',
          },
        },
        'fade-out': {
          from: {
            opacity: '1',
          },
          to: {
            opacity: '0',
          },
        },
        'ride-in-right': {
          from: {
            transform: 'translateX(100%)',
            opacity: '0',
          },
          to: {
            transform: 'translateX(0)',
            opacity: '1',
          },
        },
        'ride-in-left': {
          from: {
            transform: 'translateX(-100%)',
            opacity: '0',
          },
          to: {
            transform: 'translateX(0)',
            opacity: '1',
          },
        },
        fade: {
          '0%': {
            opacity: '1',
            height: '100%',
          },
          '50%': {
            opacity: '1',
            height: '100%',
          },
          '100%': {
            opacity: '0',
            height: '0',
          },
        },
        indicator: {
          '0%': {
            width: '0%',
            height: '0%',
            opacity: '0',
          },
          '15%': {
            opacity: '1',
          },
          '100%': {
            width: '100%',
            height: '100%',
            opacity: '0',
          },
        },
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        // scoring loader animation
        'pulse-and-pump': {
          '0%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
            transform: 'translate(-50%, -50%) scale(1)',
          },
          '6.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 52.8%)',
            transform: 'translate(-50%, -50%) scale(0.95)',
          },
          '12.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 39.6%)',
            transform: 'translate(-50%, -50%) scale(.9)',
          },
          '18.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 26.4%)',
            transform: 'translate(-50%, -50%) scale(.85)',
          },
          '25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 13.2%)',
            transform: 'translate(-50%, -50%) scale(.8)',
          },
          '31.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 0%)',
            transform: 'translate(-50%, -50%) scale(.75)',
          },
          '37.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 13.2%)',
            transform: 'translate(-50%, -50%) scale(.8)',
          },
          '43.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 26.4%)',
            transform: 'translate(-50%, -50%) scale(.85)',
          },
          '50%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 39.6%)',
            transform: 'translate(-50%, -50%) scale(.9)',
          },
          '56.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 52.8%)',
            transform: 'translate(-50%, -50%) scale(.95)',
          },
          '62.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
            transform: 'translate(-50%, -50%) scale(1)',
          },
          '68.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '81.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '87.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '93.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '100%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
        },
        'pulse-opacity': {
          '0%, 100%': {
            opacity: '1',
          },
          '50%': {
            opacity: '.5',
          },
        },
        pulse: {
          '0%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '6.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 9%, #FCFCFC 66%)',
          },
          '12.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 18%, #FCFCFC 66%)',
          },
          '18.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 27%, #FCFCFC 66%)',
          },
          '25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 36%, #FCFCFC 66%)',
          },
          '31.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 45%, #FCFCFC 66%)',
          },
          '37.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 36%, #FCFCFC 66%)',
          },
          '43.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 27%, #FCFCFC 66%)',
          },
          '50%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 18%, #FCFCFC 66%)',
          },
          '56.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 9%, #FCFCFC 66%)',
          },
          '62.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 #FCFCFC 66%)',
          },
          '68.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '81.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '87.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '93.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
          '100%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FCFCFC 66%)',
          },
        },
        'slow-fade-pulse': {
          '0%': {
            background:
              'radial-gradient(circle at center, #E2E2E2, #FFFFFF 70%)',
          },
          '3.125%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 10%, #FFFFFF 70%)',
          },
          '6.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 20%, #FFFFFF 70%)',
          },
          '9.375%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 30%, #FFFFFF 70%)',
          },
          '12.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 40%, #FFFFFF 70%)',
          },
          '15.625%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 40%, #FFFFFF 70%)',
          },
          '21.875%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 40%, #FFFFFF 70%)',
          },
          '25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 38%, #FFFFFF 70%)',
          },
          '31.25%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 36%, #FFFFFF 70%)',
          },
          '37.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 34%, #FFFFFF 70%)',
          },
          '43.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 32%, #FFFFFF 70%)',
          },
          '46.875%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 30%, #FFFFFF 70%)',
          },
          '50%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 28%, #FFFFFF 70%)',
          },
          '53.125%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 26%, #FFFFFF 70%)',
          },
          '62.5%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 24%, #FFFFFF 70%)',
          },
          '65.625%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 22%, #FFFFFF 70%)',
          },
          '68.75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 20%, #FFFFFF 70%)',
          },
          '71.875%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 18%, #FFFFFF 70%)',
          },
          '75%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 16%, #FFFFFF 70%)',
          },
          '100%': {
            background:
              'radial-gradient(circle at center, #E2E2E2 0%, #FFFFFF 70%)',
          },
        },
        'rotate-user': {
          '0%': {
            transform: 'translate(-50%, -50%) rotate(-90deg)',
          },
          '12.39%': {
            transform: 'translate(-50%, -50%) rotate(-90deg)',
          },
          '16.66%': {
            transform: 'translate(-50%, -50%) rotate(30deg)',
          },
          '45.71%': {
            transform: 'translate(-50%, -50%) rotate(30deg)',
          },
          '49.98%': {
            transform: 'translate(-50%, -50%) rotate(180deg)',
          },
          '79.48%': {
            transform: 'translate(-50%, -50%) rotate(180deg)',
          },
          '83.75%': {
            transform: 'translate(-50%, -50%) rotate(270deg)',
          },
          '100%': {
            transform: 'translate(-50%, -50%) rotate(270deg)',
          },
        },
        'rotate-reverse-user': {
          '0%': {
            transform: 'rotate(90deg)',
          },
          '12.39%': {
            transform: 'rotate(90deg)',
          },
          '16.66%': {
            transform: 'rotate(-30deg)',
          },
          '45.71%': {
            transform: 'rotate(-30deg)',
          },
          '49.98%': {
            transform: 'rotate(-180deg)',
          },
          '79.48%': {
            transform: 'rotate(-180deg)',
          },
          '83.75%': {
            transform: 'rotate(-270deg)',
          },
          '100%': {
            transform: 'rotate(-270deg)',
          },
        },
        'rotate-euro': {
          '0%': {
            transform: 'translate(-50%, -50%) rotate(90deg)',
          },
          '29.05%': {
            transform: 'translate(-50%, -50%) rotate(90deg)',
          },
          '33.32%': {
            transform: 'translate(-50%, -50%) rotate(180deg)',
          },
          '45.57%': {
            transform: 'translate(-50%, -50%) rotate(180deg)',
          },
          '49.98%': {
            transform: 'translate(-50%, -50%) rotate(245deg)',
          },
          '62.37%': {
            transform: 'translate(-50%, -50%) rotate(245deg)',
          },
          '79.03%': {
            transform: 'translate(-50%, -50%) rotate(245deg)',
          },
          '83.3%': {
            transform: 'translate(-50%, -50%) rotate(300deg)',
          },
          '95.69%': {
            transform: 'translate(-50%, -50%) rotate(300deg)',
          },
          '100%': {
            transform: 'translate(-50%, -50%) rotate(450deg)',
          },
        },
        'rotate-reverse-euro': {
          '0%': {
            transform: 'rotate(-90deg)',
          },
          '29.05%': {
            transform: 'rotate(-90deg)',
          },
          '33.32%': {
            transform: 'rotate(-180deg)',
          },
          '45.57%': {
            transform: 'rotate(-180deg)',
          },
          '49.98%': {
            transform: 'rotate(-245deg)',
          },
          '62.37%': {
            transform: 'rotate(-245deg)',
          },
          '79.03%': {
            transform: 'rotate(-245deg)',
          },
          '83.3%': {
            transform: 'rotate(-300deg)',
          },
          '95.69%': {
            transform: 'rotate(-300deg)',
          },
          '100%': {
            transform: 'rotate(-450deg)',
          },
        },
        'rotate-bank': {
          '0%': {
            transform: 'translate(-50%, -50%) rotate(0deg)',
          },
          '45.71%': {
            transform: 'translate(-50%, -50%) rotate(0deg)',
          },
          '49.98%': {
            transform: 'translate(-50%, -50%) rotate(45deg)',
          },
          '62.37%': {
            transform: 'translate(-50%, -50%) rotate(45deg)',
          },
          '66.64%': {
            transform: 'translate(-50%, -50%) rotate(170deg)',
          },
          '79.03%': {
            transform: 'translate(-50%, -50%) rotate(170deg)',
          },
          '83.3%': {
            transform: 'translate(-50%, -50%) rotate(245deg)',
          },
          '95.69%': {
            transform: 'translate(-50%, -50%) rotate(245deg)',
          },
          '100%': {
            transform: 'translate(-50%, -50%) rotate(360deg)',
          },
        },
        'rotate-reverse-bank': {
          '0%': {
            transform: 'rotate(0deg)',
          },
          '45.71%': {
            transform: 'rotate(0deg)',
          },
          '49.98%': {
            transform: 'rotate(-45deg)',
          },
          '62.37%': {
            transform: 'rotate(-45deg)',
          },
          '66.64%': {
            transform: 'rotate(-170deg)',
          },
          '79.03%': {
            transform: 'rotate(-170deg)',
          },
          '83.3%': {
            transform: 'rotate(-245deg)',
          },
          '95.69%': {
            transform: 'rotate(-245deg)',
          },
          '100%': {
            transform: 'rotate(-360deg)',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        loader: 'loader 0.6s infinite alternate',
        'fade-out': 'fade-out 2s ease 0s 1 normal forwards',
        'ride-in-right': 'ride-in-right 2s ease 0s 1 normal forwards',
        'ride-in-left': 'ride-in-left 2s ease 0s 1 normal forwards',
        fade: 'fade 1.25s ease-out forwards',
        indicator: 'indicator 2s ease-in-out infinite',
        // circle loader animation
        'pulse-and-pump':
          'pulse-and-pump .5s cubic-bezier(.21, .35, .44, .99) infinite',
        pulse: 'pulse .3s cubic-bezier(.21, .35, .44, .99) .4s infinite',
        'pulse-2': 'pulse .3s cubic-bezier(.21, .35, .44, .99) .7s infinite',
        'pulse-3':
          'slow-fade-pulse 2s cubic-bezier(0, .85, 0, .75) .9s infinite',
        'rotate-user': 'rotate-user 23.4s ease-in-out 3.9s infinite',
        'rotate-reverse-user':
          'rotate-reverse-user 23.4s ease-in-out 3.9s infinite',
        'rotate-euro': 'rotate-euro 23.4s ease-in-out 3.9s infinite',
        'rotate-reverse-euro':
          'rotate-reverse-euro 23.4s ease-in-out 3.9s infinite',
        'rotate-bank': 'rotate-bank 23.4s ease-in-out 3.9s infinite',
        'rotate-reverse-bank':
          'rotate-reverse-bank 23.4s ease-in-out 3.9s infinite',
        'pulse-opacity':
          'pulse-opacity 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'lds-spinner': 'lds-spinner 1.2s linear infinite',
      },
    },
  },
  plugins: [tailwindcss_animate, tailwindcss_grid_areas, tailwindcss()],
} satisfies Config;

export default config;
